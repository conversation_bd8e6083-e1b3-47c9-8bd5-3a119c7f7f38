import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { Barcode, ClipboardCheck, Package, CheckCircle, Wrench } from 'lucide-react-native';
import { router } from 'expo-router';

import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

export default function ScanOptions() {
  const { user } = useAuth();
  const userRole = user?.role || 'admin'; // Default to admin if no role is provided

  const handleScan = (type: string) => {
    router.push(`/scan/${type}`);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Options de Scan</Text>

      {/* Admin sees all options */}
      {userRole === 'admin' && (
        <>
          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <Barcode size={24} color="#3b82f6" />
              <Text style={styles.cardTitle}><PERSON><PERSON></Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner n'importe quel code-barres pour l'identifier dans le système
            </Text>
            <Button
              title="Scanner Code-barres"
              onPress={() => handleScan('general')}
              style={styles.button}
            />
          </Card>

          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <ClipboardCheck size={24} color="#8b5cf6" />
              <Text style={styles.cardTitle}>Lancer OF</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner un ordre de fabrication pour le lancer en production
            </Text>
            <Button
              title="Scanner OF"
              onPress={() => handleScan('of')}
              style={styles.button}
              variant="secondary"
            />
          </Card>

          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <Package size={24} color="#10b981" />
              <Text style={styles.cardTitle}>Scanner Paquet</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner un paquet pour la gestion des inventaires
            </Text>
            <Button
              title="Scanner Paquet"
              onPress={() => handleScan('packet')}
              style={styles.button}
              variant="outline"
            />
          </Card>

          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <CheckCircle size={24} color="#8b5cf6" />
              <Text style={styles.cardTitle}>Contrôle Fin de Chaîne</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner un paquet pour effectuer un contrôle de qualité en fin de chaîne
            </Text>
            <Button
              title="Contrôle Fin de Chaîne"
              onPress={() => handleScan('ctrl-fin-ch')}
              style={styles.button}
              variant="secondary"
            />
          </Card>

          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <Wrench size={24} color="#10b981" />
              <Text style={styles.cardTitle}>Finition Terminée</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner un OF pour marquer sa finition comme terminée
            </Text>
            <Button
              title="Finition Terminée"
              onPress={() => handleScan('finition')}
              style={styles.button}
              variant="outline"
            />
          </Card>
        </>
      )}

      {/* Responsable_chaine sees Contrôler Colis, Bloquer/Débloquer Packet, and Lancer OF options */}
      {userRole === 'responsable_chaine' && (
        <>
          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <CheckCircle size={24} color="#8b5cf6" />
              <Text style={styles.cardTitle}>Contrôler les Colis</Text>
            </View>
            <Text style={styles.cardDescription}>
              Contrôle les colis avant de lancer OF pour conformité
            </Text>
            <Button
              title="Contrôler les Colis"
              onPress={() => handleScan('colis')}
              style={styles.button}
              variant="secondary"
            />
          </Card>

          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <Package size={24} color="#f97316" />
              <Text style={styles.cardTitle}>Bloquer / Débloquer Paquet</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner un paquet pour le bloquer ou le débloquer
            </Text>
            <Button
              title="Bloquer / Débloquer"
              onPress={() => handleScan('toggle-block')}
              style={styles.button}
              variant="outline"
            />
          </Card>

          <Card style={styles.card}>
            <View style={styles.cardHeader}>
              <ClipboardCheck size={24} color="#000440" />
              <Text style={styles.cardTitle}>Lancer OF</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scanner OF pour le lancer
            </Text>
            <Button
              title="Scanner OF"
              onPress={() => handleScan('of')}
              style={styles.button}
              variant="primary"
            />
          </Card>
        </>
      )}

      {/* Controlleur_fin_chaine only sees Control Fin Chaine option */}
      {userRole === 'controlleur_fin_chaine' && (
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <CheckCircle size={24} color="#8b5cf6" />
            <Text style={styles.cardTitle}>Contrôle Fin de Chaîne</Text>
          </View>
          <Text style={styles.cardDescription}>
            Scanner un paquet pour effectuer un contrôle de qualité en fin de chaîne
          </Text>
          <Button
            title="Contrôle Fin de Chaîne"
            onPress={() => handleScan('ctrl-fin-ch')}
            style={styles.button}
            variant="secondary"
          />
        </Card>
      )}

      {/* Ouvrier_machine only sees Scan Packet option */}
      {userRole === 'ouvrier_machine' && (
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Package size={24} color="#10b981" />
            <Text style={styles.cardTitle}>Scanner Paquet</Text>
          </View>
          <Text style={styles.cardDescription}>
            Scanner un paquet pour la gestion des inventaires
          </Text>
          <Button
            title="Scanner Paquet"
            onPress={() => handleScan('packet')}
            style={styles.button}
            variant="primary" // Changed to primary to make it more prominent
          />
        </Card>
      )}

      {/* Ouvrier_finnition only sees Finition Terminee option */}
      {userRole === 'ouvrier_finnition' && (
        <Card style={styles.card}>
          <View style={styles.cardHeader}>
            <Wrench size={24} color="#10b981" />
            <Text style={styles.cardTitle}>Finition Terminée</Text>
          </View>
          <Text style={styles.cardDescription}>
            Scanner un OF pour marquer sa finition comme terminée
          </Text>
          <Button
            title="Finition Terminée"
            onPress={() => handleScan('finition')}
            style={styles.button}
            variant="outline"
          />
        </Card>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 16,
  },
  card: {
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  cardDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
  },
  button: {
    marginTop: 8,
  },
});