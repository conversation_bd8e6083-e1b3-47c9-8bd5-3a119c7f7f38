import { useAuth } from '@/context/AuthContext';
import { Slot, Redirect, usePathname } from 'expo-router';
import React, { useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { handleTokenExpiration } from '@/utils/api';
import { decodeJWT } from '@/utils/jwtDecode';

export default function ProtectedLayout() {
  const { user, isLoading } = useAuth();
  const pathname = usePathname();
  const [isTokenValid, setIsTokenValid] = React.useState(true);

  // Synchronous token validation function
  const validateTokenSync = () => {
    try {
      const token = AsyncStorage.getItem('auth_token');
      if (!token) {
        console.log('No token found in storage');
        return false;
      }

      // Use Promise.resolve to handle the async result synchronously
      return Promise.resolve(token).then(tokenValue => {
        if (!tokenValue) return false;

        // Validate token
        const decodedToken = decodeJWT(tokenValue);

        // Check if token is invalid or expired
        if (!decodedToken ||
            (decodedToken.exp && decodedToken.exp < Math.floor(Date.now() / 1000))) {
          console.log('Token invalid or expired in protected layout check');
          return false;
        }

        return true;
      });
    } catch (error) {
      console.error('Error validating token in protected layout:', error);
      return false;
    }
  };

  // Check token validity on every navigation/render
  useEffect(() => {
    const checkTokenAndRedirect = async () => {
      try {
        // Only check if user is logged in
        if (user) {
          const token = await AsyncStorage.getItem('auth_token');

          if (token) {
            // Validate token
            const decodedToken = decodeJWT(token);

            // Check if token is invalid or expired
            if (!decodedToken ||
                (decodedToken.exp && decodedToken.exp < Math.floor(Date.now() / 1000))) {
              console.log('Token invalid or expired in protected layout check');
              setIsTokenValid(false);
              handleTokenExpiration();
              return;
            }
          } else {
            setIsTokenValid(false);
            handleTokenExpiration();
            return;
          }
        }

        setIsTokenValid(true);
      } catch (error) {
        console.error('Error validating token in protected layout:', error);
        setIsTokenValid(false);
        handleTokenExpiration();
      }
    };

    checkTokenAndRedirect();
  }, [pathname, user]); // Re-run on navigation or user change

  if (isLoading) return null;

  // If not logged in or token is invalid, redirect to login
  if (!user || !isTokenValid) {
    console.log('Redirecting to login from protected layout - User:', !!user, 'Token valid:', isTokenValid);
    return <Redirect href="/login" />;
  }

  // Special handling for restricted roles
  if (user.role === 'responsable_chaine') {
    // Allow access only to scan page and profile page
    const allowedPaths = ['/scan', '/profile', '/scan/of', '/scan/colis', '/scan/toggle-block', '/control-colis'];
    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path));

    // If trying to access a page they shouldn't, redirect to scan
    if (!isAllowedPath) {
      return <Redirect href="/scan" />;
    }
  }

  if (user.role === 'ouvrier_machine') {
    // Allow access only to scan page and profile page
    const allowedPaths = ['/scan', '/profile', '/scan/packet'];
    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path));

    // If trying to access a page they shouldn't, redirect to scan
    if (!isAllowedPath) {
      return <Redirect href="/scan" />;
    }
  }

  // Special handling for controlleur_fin_chaine role
  if (user.role === 'controlleur_fin_chaine') {
    // Allow access only to ctrl-fin-ch page and profile page
    const allowedPaths = ['/ctrl-fin-ch', '/profile', '/scan/ctrl-fin-ch'];
    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path));

    // If trying to access a page they shouldn't, redirect to ctrl-fin-ch
    if (!isAllowedPath) {
      return <Redirect href="/ctrl-fin-ch" />;
    }
  }

  // Special handling for ouvrier_finnition role
  if (user.role === 'ouvrier_finnition') {
    // Allow access only to finition page and profile page
    const allowedPaths = ['/finition', '/profile', '/scan/finition'];
    const isAllowedPath = allowedPaths.some(path => pathname.startsWith(path));

    // If trying to access a page they shouldn't, redirect to finition
    if (!isAllowedPath) {
      return <Redirect href="/finition" />;
    }
  }

  return <Slot />;
}
