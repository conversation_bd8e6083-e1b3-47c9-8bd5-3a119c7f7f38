import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { ScanLine, Package, Clock, Shirt, CheckCircle } from 'lucide-react-native';

import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { apiRequest } from '@/utils/api';

interface Article {
  _id: string;
  name: string;
  reference: string;
  model:any;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: 'pending' | 'in_progress' | 'finishing' | 'completed' | 'canceled';
  totalPieces: number;
  article: Article;
  createdAt: string;
  scans: Array<{
    type: 'EM' | 'SM' | 'SF';
    time: string;
  }>;
}

const translateStatus = (status: Order['status']) => {
  switch (status) {
    case 'pending':
      return 'En attente';
    case 'in_progress':
      return 'En cours';
    case 'finishing':
      return 'Finition';
    case 'completed':
      return 'Terminé';
    case 'canceled':
      return 'Annulé';
    default:
      return status;
  }
};

export default function HomeScreen() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async (showLoadingSpinner = true) => {
    if (showLoadingSpinner) {
      setIsLoading(true);
    }

    try {
      setError(null);
      const response = await apiRequest<Order[]>('/orders');

      if (response.success && response.data) {
        setOrders(response.data);
      } else {
        setError(response.error || 'Failed to load orders');
      }
    } catch (err) {
      console.error('Error fetching orders:', err);
      setError('Failed to load orders');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);


  const onRefresh = () => {
    setIsRefreshing(true);
    fetchOrders(false);
  };

  const handleScanPress = () => {
    router.push('/scan/of');
  };

  const handleScanColisPress = () => {
    router.push('/scan/colis');
  };

  const handleOrderPress = (orderId: string) => {
    router.push(`/order/${orderId}`);
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'in_progress':
        return '#3b82f6';
      case 'finishing':
        return '#8b5cf6';
      case 'completed':
        return '#10b981';
      case 'canceled':
        return '#ef4444';
      default:
        return '#64748b';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  if (isLoading && !isRefreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Chargement des ordres de fabrication...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Racine Mode Scanner</Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            colors={['#3b82f6']}
            tintColor="#3b82f6"
          />
        }
      >
        <View style={styles.actionSection}>
          <Button
            title="Lancer OF"
            onPress={handleScanPress}
            style={styles.scanButton}
            icon={<ScanLine size={24} color="#ffffff" style={styles.buttonIcon} />}
          />
          <Button
            title="Scan Colis"
            onPress={handleScanColisPress}
            style={styles.scanColisButton}
            icon={<CheckCircle size={24} color="#ffffff" style={styles.buttonIcon} />}
          />
        </View>

        <View style={styles.ordersSection}>
          <Text style={styles.sectionTitle}>Ordres de Fabrication</Text>

          {error ? (
            <Card style={styles.errorCard}>
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={() => fetchOrders()}
              >
                <Text style={styles.retryText}>Réessayer</Text>
              </TouchableOpacity>
            </Card>
          ) : orders.length === 0 ? (
            <Card style={styles.emptyCard}>
              <Package size={48} color="#64748b" />
              <Text style={styles.emptyText}>Aucun ordre de fabrication</Text>
            </Card>
          ) : (
            orders.map((order) => (
              <TouchableOpacity
                key={order._id}
                onPress={() => handleOrderPress(order._id)}
              >
                <Card style={styles.orderCard}>
                  <View style={styles.orderHeader}>
                    <Text style={styles.orderNumber}>OF N°{order.orderNumber}</Text>
                    <View style={[
                      styles.statusBadge,
                      { backgroundColor: getStatusColor(order.status) }
                    ]}>
                      <Text style={styles.statusText}>
                        {translateStatus(order.status)}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.orderDetails}>
                    <Text style={styles.articleName}>
                      {order.article.name}
                    </Text>
                    <View style={{ justifyContent:"center", alignItems:"center"}}>
                      <Shirt size={30} color={getStatusColor(order.status)}/>
                    <Text style={[styles.articleRef,{}]}>
                      {order?.article?.model}
                    </Text>
                    </View>

                  </View>

                  <View style={styles.orderFooter}>
                    <View style={styles.footerItem}>
                      <Package size={16} color="#64748b" />
                      <Text style={styles.footerText}>
                        {order.totalPieces} pièces
                      </Text>
                    </View>
                    <View style={styles.footerItem}>
                      <Clock size={16} color="#64748b" />
                      <Text style={styles.footerText}>
                        {formatDate(order.createdAt)}
                      </Text>
                    </View>
                  </View>
                </Card>
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#1e293b',
  },
  content: {
    flex: 1,
  },
  actionSection: {
    padding: 16,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#000440',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  scanColisButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8b5cf6',
    borderRadius: 12,
    padding: 16,
  },
  buttonIcon: {
    marginRight: 8,
  },
  ordersSection: {
    padding: 16,
    paddingTop: 0,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000440',
    marginBottom: 12,
  },
  orderCard: {
    marginBottom: 12,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1e293b',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#ffffff',
    textTransform: 'capitalize',
  },
  orderDetails: {
    marginBottom: 12,
  },
  articleName: {
    fontSize: 14,
    color: '#1e293b',
    marginBottom: 4,
  },
  articleRef: {
    fontSize: 18,
    color: '#64748b',
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    paddingTop: 12,
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerText: {
    fontSize: 18,
    color: '#64748b',
    marginLeft: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b',
  },
  errorCard: {
    padding: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    marginBottom: 12,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyCard: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#64748b',
    marginTop: 12,
  },
});